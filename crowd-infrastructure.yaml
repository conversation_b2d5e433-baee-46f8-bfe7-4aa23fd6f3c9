AWSTemplateFormatVersion: '2010-09-09'
Description: 'Atlassian Crowd Data Center 5.3 Infrastructure - Dev/Test Environment'

Parameters:
  VpcId:
    Type: AWS::EC2::VPC::Id
    Description: VPC ID where resources will be deployed

  ALBSubnetIds:
    Type: List<AWS::EC2::Subnet::Id>
    Description: Private subnet IDs for internal Application Load Balancer (minimum 2)

  EC2SubnetIds:
    Type: List<AWS::EC2::Subnet::Id>
    Description: Private subnet IDs for EC2 instances

  RDSSubnetIds:
    Type: List<AWS::EC2::Subnet::Id>
    Description: Private subnet IDs for RDS (minimum 2 for RDS subnet group)
    
  AmiId:
    Type: AWS::EC2::Image::Id
    Description: AMI ID for EC2 instance (Amazon Linux 2)
    Default: ami-0abcdef1234567890
    
  KeyPairName:
    Type: AWS::EC2::KeyPair::KeyName
    Description: EC2 Key Pair for SSH access
    
  DomainName:
    Type: String
    Description: Domain name for Crowd (e.g., crowd.example.com)
    
  Environment:
    Type: String
    Default: dev
    Description: Environment name for resource tagging
    AllowedValues: [dev, test, staging]

Resources:
  # Security Groups
  CrowdSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Security group for Crowd EC2 instance
      VpcId: !Ref VpcId
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 8095
          ToPort: 8095
          SourceSecurityGroupId: !Ref ALBSecurityGroup
          Description: HTTP from ALB
        - IpProtocol: tcp
          FromPort: 22
          ToPort: 22
          CidrIp: 10.0.0.0/8
          Description: SSH access from VPC
      Tags:
        - Key: Name
          Value: !Sub 'crowd-${Environment}-ec2-sg'
        - Key: Environment
          Value: !Ref Environment

  ALBSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Security group for internal Application Load Balancer
      VpcId: !Ref VpcId
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 80
          ToPort: 80
          CidrIp: 10.0.0.0/8
          Description: HTTP from internal network
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp: 10.0.0.0/8
          Description: HTTPS from internal network
      Tags:
        - Key: Name
          Value: !Sub 'crowd-${Environment}-alb-sg'
        - Key: Environment
          Value: !Ref Environment

  RDSSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Security group for RDS Aurora PostgreSQL
      VpcId: !Ref VpcId
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 5432
          ToPort: 5432
          SourceSecurityGroupId: !Ref CrowdSecurityGroup
          Description: PostgreSQL from Crowd EC2
      Tags:
        - Key: Name
          Value: !Sub 'crowd-${Environment}-rds-sg'
        - Key: Environment
          Value: !Ref Environment

  # RDS Subnet Group
  RDSSubnetGroup:
    Type: AWS::RDS::DBSubnetGroup
    Properties:
      DBSubnetGroupDescription: Subnet group for Crowd RDS Aurora
      SubnetIds: !Ref RDSSubnetIds
      Tags:
        - Key: Name
          Value: !Sub 'crowd-${Environment}-db-subnet-group'
        - Key: Environment
          Value: !Ref Environment

  # RDS Aurora Cluster
  AuroraCluster:
    Type: AWS::RDS::DBCluster
    Properties:
      DBClusterIdentifier: !Sub 'crowd-${Environment}-aurora'
      Engine: aurora-postgresql
      EngineVersion: '14.9'
      MasterUsername: crowduser
      ManageMasterUserPassword: true
      DatabaseName: crowddb
      DBSubnetGroupName: !Ref RDSSubnetGroup
      VpcSecurityGroupIds:
        - !Ref RDSSecurityGroup
      BackupRetentionPeriod: 7
      PreferredBackupWindow: '03:00-04:00'
      PreferredMaintenanceWindow: 'sun:04:00-sun:05:00'
      Tags:
        - Key: Name
          Value: !Sub 'crowd-${Environment}-aurora-cluster'
        - Key: Environment
          Value: !Ref Environment

  # RDS Aurora Instance
  AuroraInstance:
    Type: AWS::RDS::DBInstance
    Properties:
      DBInstanceIdentifier: !Sub 'crowd-${Environment}-aurora-instance'
      DBClusterIdentifier: !Ref AuroraCluster
      DBInstanceClass: db.t3.medium
      Engine: aurora-postgresql
      PubliclyAccessible: false
      Tags:
        - Key: Name
          Value: !Sub 'crowd-${Environment}-aurora-instance'
        - Key: Environment
          Value: !Ref Environment

  # IAM Role for EC2
  CrowdEC2Role:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub 'crowd-${Environment}-ec2-role'
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: ec2.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws-us-gov:iam::aws:policy/CloudWatchAgentServerPolicy
        - arn:aws-us-gov:iam::aws:policy/AmazonSSMManagedInstanceCore
      Policies:
        - PolicyName: CrowdSecretsAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - secretsmanager:GetSecretValue
                Resource: !Ref AuroraCluster
      Tags:
        - Key: Environment
          Value: !Ref Environment

  CrowdInstanceProfile:
    Type: AWS::IAM::InstanceProfile
    Properties:
      InstanceProfileName: !Sub 'crowd-${Environment}-instance-profile'
      Roles:
        - !Ref CrowdEC2Role

  # EC2 Instance
  CrowdInstance:
    Type: AWS::EC2::Instance
    Properties:
      ImageId: !Ref AmiId
      InstanceType: t3.small
      KeyName: !Ref KeyPairName
      IamInstanceProfile: !Ref CrowdInstanceProfile
      SecurityGroupIds:
        - !Ref CrowdSecurityGroup
      SubnetId: !Select [0, !Ref EC2SubnetIds]
      UserData:
        Fn::Base64: !Sub |
          #!/bin/bash
          yum update -y
          yum install -y java-11-amazon-corretto-headless wget
          
          # Create crowd user
          useradd -m -s /bin/bash crowd
          
          # Download and install Crowd
          cd /opt
          wget https://product-downloads.atlassian.com/software/crowd/downloads/atlassian-crowd-5.3.0.tar.gz
          tar -xzf atlassian-crowd-5.3.0.tar.gz
          mv atlassian-crowd-5.3.0 crowd
          chown -R crowd:crowd /opt/crowd
          
          # Create Crowd home directory
          mkdir -p /var/atlassian/application-data/crowd
          chown -R crowd:crowd /var/atlassian/application-data/crowd
          
          # Install CloudWatch agent
          wget https://s3.amazonaws.com/amazoncloudwatch-agent/amazon_linux/amd64/latest/amazon-cloudwatch-agent.rpm
          rpm -U ./amazon-cloudwatch-agent.rpm
          
      Tags:
        - Key: Name
          Value: !Sub 'crowd-${Environment}-instance'
        - Key: Environment
          Value: !Ref Environment

  # SSL Certificate
  SSLCertificate:
    Type: AWS::CertificateManager::Certificate
    Properties:
      DomainName: !Ref DomainName
      ValidationMethod: DNS
      Tags:
        - Key: Name
          Value: !Sub 'crowd-${Environment}-ssl-cert'
        - Key: Environment
          Value: !Ref Environment

  # Application Load Balancer
  ApplicationLoadBalancer:
    Type: AWS::ElasticLoadBalancingV2::LoadBalancer
    Properties:
      Name: !Sub 'crowd-${Environment}-alb'
      Type: application
      Scheme: internet-facing
      SecurityGroups:
        - !Ref ALBSecurityGroup
      Subnets: !Ref PublicSubnetIds
      Tags:
        - Key: Name
          Value: !Sub 'crowd-${Environment}-alb'
        - Key: Environment
          Value: !Ref Environment

  # Target Group
  CrowdTargetGroup:
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Properties:
      Name: !Sub 'crowd-${Environment}-tg'
      Port: 8095
      Protocol: HTTP
      VpcId: !Ref VpcId
      HealthCheckPath: /crowd/status
      HealthCheckProtocol: HTTP
      HealthCheckIntervalSeconds: 30
      HealthCheckTimeoutSeconds: 5
      HealthyThresholdCount: 2
      UnhealthyThresholdCount: 3
      Targets:
        - Id: !Ref CrowdInstance
          Port: 8095
      Tags:
        - Key: Name
          Value: !Sub 'crowd-${Environment}-target-group'
        - Key: Environment
          Value: !Ref Environment

  # HTTPS Listener
  HTTPSListener:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      DefaultActions:
        - Type: forward
          TargetGroupArn: !Ref CrowdTargetGroup
      LoadBalancerArn: !Ref ApplicationLoadBalancer
      Port: 443
      Protocol: HTTPS
      Certificates:
        - CertificateArn: !Ref SSLCertificate

  # HTTP Listener (redirect to HTTPS)
  HTTPListener:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      DefaultActions:
        - Type: redirect
          RedirectConfig:
            Protocol: HTTPS
            Port: 443
            StatusCode: HTTP_301
      LoadBalancerArn: !Ref ApplicationLoadBalancer
      Port: 80
      Protocol: HTTP

Outputs:
  LoadBalancerDNS:
    Description: DNS name of the Application Load Balancer
    Value: !GetAtt ApplicationLoadBalancer.DNSName
    Export:
      Name: !Sub '${AWS::StackName}-LoadBalancerDNS'
      
  CrowdInstanceId:
    Description: Instance ID of the Crowd EC2 instance
    Value: !Ref CrowdInstance
    Export:
      Name: !Sub '${AWS::StackName}-CrowdInstanceId'
      
  DatabaseEndpoint:
    Description: Aurora PostgreSQL cluster endpoint
    Value: !GetAtt AuroraCluster.Endpoint.Address
    Export:
      Name: !Sub '${AWS::StackName}-DatabaseEndpoint'
      
  DatabaseSecretArn:
    Description: ARN of the database master user secret
    Value: !Ref AuroraCluster
    Export:
      Name: !Sub '${AWS::StackName}-DatabaseSecretArn'
      
  SSLCertificateArn:
    Description: ARN of the SSL certificate
    Value: !Ref SSLCertificate
    Export:
      Name: !Sub '${AWS::StackName}-SSLCertificateArn'
