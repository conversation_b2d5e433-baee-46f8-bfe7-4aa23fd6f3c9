#!/bin/bash

# Deployment script for Atlassian Crowd Data Center Infrastructure
# Update the parameter values below with your actual resource IDs

aws cloudformation create-stack \
  --stack-name crowd-dev-infrastructure \
  --template-body file://crowd-infrastructure.yaml \
  --parameters \
    ParameterKey=VpcId,ParameterValue=vpc-xxxxxxxx \
    ParameterKey=ALBSubnetIds,ParameterValue="subnet-alb1-xxxx,subnet-alb2-yyyy" \
    ParameterKey=EC2SubnetIds,ParameterValue="subnet-ec2-zzzz" \
    ParameterKey=RDSSubnetIds,ParameterValue="subnet-rds1-aaaa,subnet-rds2-bbbb" \
    ParameterKey=AmiId,ParameterValue=ami-xxxxxxxx \
    ParameterKey=DomainName,ParameterValue=crowd.internal.yourdomain.com \
    ParameterKey=Environment,ParameterValue=dev \
  --capabilities CAPABILITY_NAMED_IAM \
  --region us-gov-west-1

echo "Stack creation initiated. Monitor progress with:"
echo "aws cloudformation describe-stacks --stack-name crowd-dev-infrastructure --region us-gov-west-1"
